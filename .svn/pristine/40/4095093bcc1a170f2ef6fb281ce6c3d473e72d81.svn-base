<?php
/**
 * User: <PERSON><PERSON>
 * Date: 2025/7/24
 * Time: 16:13
 */

namespace app\service;

use app\model\User as UserModel;
use think\facade\Db;

class User
{
    /**
     * 校验用户名密码
     */
    public static function isLoginSuccess(string $username, string $password){
        $account = UserModel::where('username', $username)->find();
        if(!$account) error('该用户不存在');
        if(!password_verify($password, $account->password)) error('用户名或密码不正确');
        return $account;
    }

    /**
     * 获取用户权限菜单
     */
    public static function getAuthInfo($userId)
    {
        //查询用户信息
        $userInfo = UserModel::withoutField('password')->find($userId)->toArray();

        //查询用户拥有的角色
        $roles = Db::name('user_role')->alias('a')
            ->leftJoin('role b','b.id = a.role_id')
            ->where('user_id', $userId)
            ->field('role_id, code as roleCode')
            ->select()->toArray();

        $roleIds = [];
        foreach ($roles as $k => $role)
        {
            $roleIds[] = $role['role_id'];
            unset($roles[$k]['role_id']);
        }

        //查询角色所包含的菜单权限
        $menus_list = Db::name('role_menu')->alias('a')
            ->leftJoin('menu b','b.id = a.menu_id')
            ->whereIn('role_id',$roleIds)
            ->group('menu_id')
            ->select();

        $api_list = [];
        foreach ($menus_list as $v)
        {
            if ($v['api_path']) $api_list[] = $v['api_path'];
        }

        $userInfo['authorities'] = $menus_list;
        $userInfo['roles'] = $roles;
        $userInfo['api'] = base64_encode(json_encode($api_list));

        return $userInfo;
    }
}