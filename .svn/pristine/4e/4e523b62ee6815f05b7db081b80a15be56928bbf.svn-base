<?php
/**
 * @author: xiaobo
 * @date: 2025-06-12
 * @description: 用户角色表模型
 */

namespace app\model;

use think\Model;
use app\model\traits\MigrateTrait;

/**
 * @mixin \think\Model
 */
class UserRole extends Model
{
    use MigrateTrait;

    protected $updateTime = false;

    protected static $migrateSchema = [
        // 表配置
        'table' => [
            'engine' => 'InnoDB',
            'comment' => '用户角色表'
        ],

        // 字段
        'columns' => [
            ['user_id', 'integer', ['limit' => 10, 'null' => false, 'comment' => '用户ID']],
            ['role_id', 'integer', ['limit' => 10, 'null' => false, 'comment' => '角色ID']],
            ['create_time', 'datetime', ['default' => null, 'comment' => '创建时间']],
        ],

        // 索引
        'indexes' => [],
    ];
}