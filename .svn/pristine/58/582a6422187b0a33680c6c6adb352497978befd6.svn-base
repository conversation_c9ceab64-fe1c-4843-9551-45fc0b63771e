<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateMenuTable extends Migrator
{
    /**
     * Change Method.
     */
    public function change()
    {
        // 尝试创建表
        try {
            $table = $this->table('menu', ['engine' => 'InnoDB', 'comment' => '权限表']);
            $table->addColumn('title', 'string', ['limit' => 25, 'null' => false, 'comment' => '权限名称'])
                ->addColumn('parent_id', 'integer', ['limit' => 10, 'default' => 0, 'comment' => '父级ID'])
                ->addColumn('menu_type', 'boolean', ['default' => false, 'comment' => '菜单类型: 0 菜单, 1 按钮或列表权限'])
                ->addColumn('open_type', 'boolean', ['default' => false, 'comment' => '打开方式: 1 组件, 2 内链, 3 外链'])
                ->addColumn('icon', 'string', ['limit' => 20, 'default' => NULL, 'comment' => '图标'])
                ->addColumn('hide', 'boolean', ['default' => false, 'comment' => '是否隐藏: 0 否, 1 是'])
                ->addColumn('url_path', 'string', ['limit' => 80, 'default' => NULL, 'comment' => '路径地址'])
                ->addColumn('api_path', 'string', ['limit' => 120, 'default' => NULL, 'comment' => '接口地址'])
                ->addColumn('sort', 'integer', ['limit' => 4, 'default' => 0, 'comment' => '排序'])
                ->addColumn('authority', 'string', ['limit' => 40, 'default' => NULL, 'comment' => '权限标识'])
                ->addColumn('component', 'string', ['limit' => 20, 'default' => NULL, 'comment' => '组件'])
                ->addColumn('redirect', 'string', ['limit' => 20, 'default' => NULL, 'comment' => '重定向'])
                ->addColumn('meta', 'json', ['default' => NULL, 'comment' => '元数据'])
                ->addColumn('update_time', 'datetime', ['default' => NULL, 'comment' => '更新时间'])
                ->addColumn('create_time', 'datetime', ['default' => NULL, 'comment' => '创建时间'])
                ->create();
        } catch (\Exception $e) {
            // 表可能已经存在，尝试逐个添加缺失的字段和索引
            try {
                $table = $this->table('menu');
                $table->addColumn('title', 'string', ['limit' => 25, 'null' => false, 'comment' => '权限名称'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('parent_id', 'integer', ['limit' => 10, 'default' => 0, 'comment' => '父级ID'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('menu_type', 'boolean', ['default' => false, 'comment' => '菜单类型: 0 菜单, 1 按钮或列表权限'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('open_type', 'boolean', ['default' => false, 'comment' => '打开方式: 1 组件, 2 内链, 3 外链'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('icon', 'string', ['limit' => 20, 'default' => NULL, 'comment' => '图标'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('hide', 'boolean', ['default' => false, 'comment' => '是否隐藏: 0 否, 1 是'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('url_path', 'string', ['limit' => 80, 'default' => NULL, 'comment' => '路径地址'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('api_path', 'string', ['limit' => 120, 'default' => NULL, 'comment' => '接口地址'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('sort', 'integer', ['limit' => 4, 'default' => 0, 'comment' => '排序'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('authority', 'string', ['limit' => 40, 'default' => NULL, 'comment' => '权限标识'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('component', 'string', ['limit' => 20, 'default' => NULL, 'comment' => '组件'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('redirect', 'string', ['limit' => 20, 'default' => NULL, 'comment' => '重定向'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('meta', 'json', ['default' => NULL, 'comment' => '元数据'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('update_time', 'datetime', ['default' => NULL, 'comment' => '更新时间'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('menu');
                $table->addColumn('create_time', 'datetime', ['default' => NULL, 'comment' => '创建时间'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
        }
    }
}