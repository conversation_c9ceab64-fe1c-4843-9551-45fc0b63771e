<?php
/**
 * User: <PERSON>bo
 * Date: 2021/9/24
 * Time: 13:26
 */

namespace extend;


use think\exception\ErrorException;
use think\facade\Env;

class Redis
{
    // 单例模式
    private static $instance = null;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (self::$instance === null) {

            $host = Env::get('REDIS.HOST');
            $port = Env::get('REDIS.PORT');
            $password = Env::get('REDIS.PWD');

            try {
                self::$instance = new \Redis();
                self::$instance->connect($host, $port);
                self::$instance->auth($password);
            }catch (ErrorException $e){
                throw new ErrorException('Redis连接失败:'. $e->getMessage());
            }
        }

        return self::$instance;
    }

    public static function exits($key)
    {
        return self::getInstance()->exists($key);
    }

    public static function set($key, $value)
    {
        return self::getInstance()->set($key, $value);
    }

    public static function setArray($key, $value, $expire = 0)
    {
        $result = self::getInstance()->set($key, json_encode($value));

        if ($expire > 0) {
            self::$instance->expire($key, $expire);
        }

        return $result;
    }

    public static function setex($key, $ttl, $value)
    {
        return self::getInstance()->setex($key, $ttl, $value);
    }

    public static function get($key)
    {
        return self::getInstance()->get($key);
    }

    public static function getArray($key)
    {
        $value = self::getInstance()->get($key);

        return $value !== false ? json_decode($value, true) : $value;
    }

    public static function del($key)
    {
        return self::getInstance()->del($key);
    }

    public static function incr($key)
    {
        return self::getInstance()->incr($key);
    }

}
