<?php
/**
 * User: <PERSON><PERSON>
 * Date: 2024/3/24
 * Time: 14:32
 */

namespace app;


use app\exception\BaseException;
use app\middleware\AccessAuth;
use app\middleware\JWTAuthAndRefresh;
use app\middleware\OperationsLog;
use app\middleware\ResponseHeader;
use think\App;
use think\Model;

abstract class WMS extends BaseController
{
    //继承者类名
    public $instanceClassName = null;
    //响应内容
    protected $response = null;

    public function __construct(App $app)
    {
        parent::__construct($app);

        //中间件
        $this->middleware = [JWTAuthAndRefresh::class, AccessAuth::class, ResponseHeader::class, OperationsLog::class];

        //获得当前调用类名称
        $controllerName = get_called_class();
        $this->instanceClassName = basename(str_replace('\\', '/', $controllerName));
    }

    public function setModelName($modelName)
    {
        $this->instanceClassName = $modelName;
    }

    /**
     * 表格分页数据列表
     */
    public function tableList()
    {
        //动态实例化同名模型
        $modelClass = '\app\model\\'.$this->instanceClassName;

        if (class_exists($modelClass)) {
            /** @var Model $instance */
            $instance = new $modelClass();
            //通用表格查询
            $search = $this->request->param() ?: [];
            $page = $this->request->get('page',0);
            $limit = $this->request->get('limit',0);
            $tableData = $instance->getTableList($search, $page, $limit);

            $this->response = $tableData;

            return success('success', $this->response);
        }

        throw new BaseException(404);
    }

    /**
     * 添加
     */
    public function add()
    {
        $modelClass = '\app\model\\'.$this->instanceClassName;

        if (class_exists($modelClass)) {
            $params = $this->request->param();
            $this->validate($params, "{$this->instanceClassName}.add");

            //动态实例化同名模型
            /** @var Model $instance */
            $instance = new $modelClass();
            $instance->add($params);

            return success('操作成功');
        }

        throw new BaseException(404);
    }

    /**
     * 编辑
     */
    public function edit()
    {
        $modelClass = '\app\model\\'.$this->instanceClassName;

        if (class_exists($modelClass)) {
            $params = $this->request->param();
            $this->validate($params, "{$this->instanceClassName}.edit");

            //动态实例化同名模型
            /** @var Model $instance */
            $instance = new $modelClass();
            $instance->edit($params['id'], $params);

            return success('操作成功',$params);
        }

        throw new BaseException(404);
    }

    /**
     * 删除
     */
    public function del()
    {
        $modelClass = '\app\model\\'.$this->instanceClassName;

        if (class_exists($modelClass)) {
            $id = $this->request->post('id');
            $this->validate(['id' => $id],['id' => 'require|number']);

            //动态实例化同名模型
            /** @var Model $instance */
            $instance = new $modelClass();
            $instance->del($id);

            return success('操作成功');
        }

        throw new BaseException(404);
    }

    /**
     * 批量删除
     */
    public function delete()
    {
        $modelClass = '\app\model\\'.$this->instanceClassName;
        if (class_exists($modelClass)) {
            $idList = $this->request->post('idList');

            $ids = [];
            foreach ($idList as $row)
            {
                $this->validate(['id' => $row['id']],['id' => 'require|number']);
                $ids[] = $row['id'];
            }

            //动态实例化同名模型
            /** @var Model $instance */
            $instance = new $modelClass();
            $instance::destroy($ids);

            return success(count($ids) > 1 ? '批量删除完成' : '删除成功');
        }

        throw new BaseException(404);
    }

    /**
     * 当前请求用户是否拥有指定角色
     */
    public function hasRole($roleName)
    {
        $payload = $this->request->payload;

        foreach ($payload->roles as $role)
        {
            if ($role->roleCode == $roleName) return true;
        }

        return null;
    }

    /**
     * 获取当前登陆用户信息
     */
    public function getUserInfo()
    {
        return $this->request->payload;
    }

}
