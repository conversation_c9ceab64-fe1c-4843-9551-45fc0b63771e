<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateUserRoleTable extends Migrator
{
    /**
     * Change Method.
     */
    public function change()
    {
        // 尝试创建表
        try {
            $table = $this->table('user_role', ['engine' => 'InnoDB', 'comment' => '用户角色表']);
            $table->addColumn('user_id', 'integer', ['limit' => 10, 'null' => false, 'comment' => '用户ID'])
                ->addColumn('role_id', 'integer', ['limit' => 10, 'null' => false, 'comment' => '角色ID'])
                ->addColumn('create_time', 'datetime', ['default' => NULL, 'comment' => '创建时间'])
                ->create();
        } catch (\Exception $e) {
            // 表可能已经存在，尝试逐个添加缺失的字段和索引
            try {
                $table = $this->table('user_role');
                $table->addColumn('user_id', 'integer', ['limit' => 10, 'null' => false, 'comment' => '用户ID'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('user_role');
                $table->addColumn('role_id', 'integer', ['limit' => 10, 'null' => false, 'comment' => '角色ID'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('user_role');
                $table->addColumn('create_time', 'datetime', ['default' => NULL, 'comment' => '创建时间'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
        }
    }
}