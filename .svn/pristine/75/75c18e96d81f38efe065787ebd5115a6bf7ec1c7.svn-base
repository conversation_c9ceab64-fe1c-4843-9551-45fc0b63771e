<?php
/**
 * User: <PERSON><PERSON>
 * Date: 2022/4/15
 * Time: 23:13
 */

namespace app\controller;

use app\WMS;
use app\extend\Captcha;
use app\service\User as UserService;
use thans\jwt\facade\JWTAuth;

class User extends WMS
{
    //获取验证码
    public function captcha()
    {
        $captcha = new Captcha();
        return success('',[
            'uuid' => $captcha->getUUid(),
            'base64' => 'data:image/png;base64,'.$captcha->getBase64Data(),
        ]);
    }

    //登陆
    public function login()
    {
        $param = $this->request->param();
        // $this->validate($param, 'User.login');

        //验证码校验
        (new Captcha())->check($param['uuid'], $param['code']);

        //账号密码校验
        $user = UserService::isLoginSuccess($param['username'], $param['password']);

        //获得用户权限
        $user_info = UserService::getAuthInfo($user->id);
        unset($user_info['authorities']);

        //缓存系统配置
        // Model::updateToCache();

        return success('登陆成功',[
            'access_token' => 'Bearer ' . JWTAuth::builder([
                'data' => $user_info
            ]),
        ]);
    }
}
