<?php
/**
 * User: <PERSON><PERSON>
 * Date: 2022/4/14
 * Time: 12:52
 */
namespace app\middleware;

use Closure;
class ResponseHeader
{
    /**
     * 自定义header头
     *
     * @param $request
     * @param Closure $next
     * @param array|null $header
     * @return mixed
     */
    public function handle($request, Closure $next, ? array $header = [])
    {
        $header['Access-Control-Expose-Headers'] = 'Authorization';
        return $next($request)->header($header);
    }
}
