{"app\\model\\Menu": {"last_updated": "2025-06-12 15:22:02", "schema": {"table": {"engine": "InnoDB", "comment": "权限表"}, "columns": [["title", "string", {"limit": 25, "null": false, "comment": "权限名称"}], ["parent_id", "integer", {"limit": 10, "default": 0, "comment": "父级ID"}], ["menu_type", "boolean", {"default": false, "comment": "菜单类型: 0 菜单, 1 按钮或列表权限"}], ["open_type", "boolean", {"default": false, "comment": "打开方式: 1 组件, 2 内链, 3 外链"}], ["icon", "string", {"limit": 20, "default": null, "comment": "图标"}], ["hide", "boolean", {"default": false, "comment": "是否隐藏: 0 否, 1 是"}], ["url_path", "string", {"limit": 80, "default": null, "comment": "路径地址"}], ["api_path", "string", {"limit": 120, "default": null, "comment": "接口地址"}], ["sort", "integer", {"limit": 4, "default": 0, "comment": "排序"}], ["authority", "string", {"limit": 40, "default": null, "comment": "权限标识"}], ["component", "string", {"limit": 20, "default": null, "comment": "组件"}], ["redirect", "string", {"limit": 20, "default": null, "comment": "重定向"}], ["meta", "json", {"default": null, "comment": "元数据"}], ["update_time", "datetime", {"default": null, "comment": "更新时间"}], ["create_time", "datetime", {"default": null, "comment": "创建时间"}]], "indexes": []}}, "app\\model\\Role": {"last_updated": "2025-06-12 15:22:02", "schema": {"table": {"engine": "InnoDB", "comment": "角色表"}, "columns": [["name", "string", {"limit": 20, "null": false, "comment": "角色名称"}], ["code", "string", {"limit": 20, "null": false, "comment": "角色标识"}], ["comments", "string", {"limit": 100, "default": null, "comment": "备注"}], ["update_time", "datetime", {"default": null, "comment": "更新时间"}], ["create_time", "datetime", {"default": null, "comment": "创建时间"}]], "indexes": [["code"]]}}, "app\\model\\RoleMenu": {"last_updated": "2025-06-12 15:22:02", "schema": {"table": {"engine": "InnoDB", "comment": "角色菜单表"}, "columns": [["role_id", "integer", {"limit": 10, "null": false, "comment": "角色ID"}], ["menu_id", "integer", {"limit": 10, "null": false, "comment": "菜单ID"}], ["create_time", "datetime", {"default": null, "comment": "创建时间"}]], "indexes": []}}, "app\\model\\User": {"last_updated": "2025-06-12 15:22:02", "schema": {"table": {"engine": "InnoDB", "comment": "用户表"}, "columns": [["username", "string", {"limit": 25, "null": false, "default": "", "comment": "用户名"}], ["nickname", "string", {"limit": 25, "null": false, "default": "", "comment": "昵称"}], ["password", "string", {"limit": 60, "null": false, "default": "", "comment": "用户密码"}], ["allow_login", "boolean", {"default": 1, "comment": "是否允许登录"}], ["last_login_ip", "string", {"limit": 20, "default": null, "comment": "最后登录IP"}], ["last_login_time", "datetime", {"default": null, "comment": "最后登录时间"}], ["update_time", "datetime", {"default": null, "comment": "更新时间"}], ["create_time", "datetime", {"default": null, "comment": "创建时间"}]], "indexes": [["username", {"unique": true}]]}}, "app\\model\\UserRole": {"last_updated": "2025-06-12 15:22:02", "schema": {"table": {"engine": "InnoDB", "comment": "用户角色表"}, "columns": [["user_id", "integer", {"limit": 10, "null": false, "comment": "用户ID"}], ["role_id", "integer", {"limit": 10, "null": false, "comment": "角色ID"}], ["create_time", "datetime", {"default": null, "comment": "创建时间"}]], "indexes": []}}}