<?php
/**
 * @author: xiaobo
 * @date: 2025-06-10
 * @description: 用户表模型
 */

namespace app\model;

use think\Model;
use app\model\traits\MigrateTrait;

/**
 * @mixin \think\Model
 */
class User extends Model
{
    use MigrateTrait;

    protected static $migrateSchema = [
        // 表配置
        'table' => [
            'engine' => 'InnoDB',
            'comment' => '用户表'
        ],

        // 字段
        'columns' => [
            ['username', 'string', ['limit' => 25, 'null' => false, 'default' => '', 'comment' => '用户名']],
            ['nickname', 'string', ['limit' => 25, 'null' => false, 'default' => '', 'comment' => '昵称']],
            ['password', 'string', ['limit' => 60, 'null' => false, 'default' => '', 'comment' => '用户密码']],
            ['allow_login', 'boolean', ['default' => 1, 'comment' => '是否允许登录']],
            ['last_login_ip', 'string', ['limit' => 20, 'default' => null, 'comment' => '最后登录IP']],
            ['last_login_time', 'datetime', ['default' => null, 'comment' => '最后登录时间']],
            ['update_time', 'datetime', ['default' => null, 'comment' => '更新时间']],
            ['create_time', 'datetime', ['default' => null, 'comment' => '创建时间']],
        ],

        // 索引
        'indexes' => [
            ['username', ['unique' => true]],  // 唯一索引
        ],
    ];
}