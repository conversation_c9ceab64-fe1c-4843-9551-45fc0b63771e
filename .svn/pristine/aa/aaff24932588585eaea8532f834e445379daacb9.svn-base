<?php
namespace app\middleware;

use think\facade\Config;

/**
 * WMS访问权限校验
 */
class AccessAuth
{
    /**
     * 处理请求
     *
     * @param $request
     * @param \Closure $next
     * @return mixed
     * @throws \app\exception\BaseException
     */
    public function handle($request, \Closure $next)
    {
        // OPTIONS请求直接返回
        if ($request->isOptions()) {
            return response();
        }

        //获取当前请求地址
        $path = '/'.$request->pathinfo();

        //ApiDoc调试
        if ($request->header('Authorization') == 'ApiDebug') {
            return $next($request);
        }

        //不需要登陆的接口不做权限校验
        if (!in_array($path, Config::get('global.no_login'))) {
            //解码token中的可访问api
            $api_list = json_decode(base64_decode($request->payload->api), true);
            //合并加入公共访问访问的api
            $api_list = array_merge($api_list, Config::get('global.public_api'));

            if (!in_array($path, $api_list)) error('无权访问',403);
        }

        return  $next($request);
    }
}
