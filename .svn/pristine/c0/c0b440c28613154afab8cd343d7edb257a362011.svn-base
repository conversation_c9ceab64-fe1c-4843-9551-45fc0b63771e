<?php
namespace app;

use app\exception\BaseException;
use thans\jwt\exception\JWTException;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\facade\Env;
use think\Response;
use Throwable;

/**
 * 应用异常处理类
 */
class ExceptionHandle extends Handle
{

    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ValidateException::class,
        BaseException::class,
        JWTException::class,
    ];

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     *
     * @access public
     * @param  Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 使用内置的方式记录异常日志
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @access public
     * @param \think\Request   $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        //系统异常
        $exception = [
            'code' => 1,
            'message' => $e->getMessage(),
            'exception' => [
                'class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]
        ];

        // 获取调用堆栈信息
        $trace = $e->getTrace();

        //定义业务代码的位置
        foreach ($trace as $cur)
        {
            if (isset($cur['file']) && strpos($cur['file'],'vendor') === false) {
                $exception['exception']['file'] = $cur['file'];
                $exception['exception']['line'] = $cur['line'];
                break;
            }
        }

        //自定义异常
        if ($e instanceof BaseException) {
            $exception = [
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
            ];
        }

        if ($e instanceof ModelNotFoundException) {
            $exception['message'] = '查询数据不存在';
        }

        //jwt异常
        if ($e instanceof JWTException) {
            $exception = [
                'code' => 401,
                'message' => $e->getMessage(),
            ];
        }

        //ApiDoc异常
        if ($e instanceof \hg\apidoc\exception\HttpException) {
            return json(
                [
                    "code" => $e->getCode(),
                    "message" => $e->getMessage(),
                ],
                $e->getStatusCode()
            );
        }

        return json($exception);

        // 其他错误交给系统处理
        //return parent::render($request, $e);
    }
}
