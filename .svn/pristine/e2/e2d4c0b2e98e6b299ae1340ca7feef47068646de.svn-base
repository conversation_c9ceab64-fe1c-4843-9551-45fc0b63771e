<?php
/**
 * User: <PERSON><PERSON>
 * Date: 2022/4/13
 * Time: 14:15
 */
namespace app\middleware;

use thans\jwt\exception\TokenBlacklistGracePeriodException;
use thans\jwt\exception\TokenExpiredException;
use thans\jwt\exception\TokenInvalidException;
use thans\jwt\middleware\BaseMiddleware;
use think\facade\Config;

/**
 * WMS登陆校验
 */
class JWTAuthAndRefresh extends BaseMiddleware
{
    /**
     * 检查token合法性，第一次过期允许通行并发放新token
     */
    public function handle($request, \Closure $next)
    {
        // OPTIONS请求直接返回
        if ($request->isOptions()) {
            return response();
        }

        //获取当前请求地址
        $path = '/'.$request->pathInfo();

        //ApiDoc调试
        if ($request->header('Authorization') == 'ApiDebug') {
            return $next($request);
        }

        //对白名单外的接口进行验证
        if (!in_array($path, Config::get('global.no_login'))) {
            try {
                //验证token
                $payload = $this->auth->auth();
                //携带payload
                $request->payload = $payload['data']->getValue();
            } catch (TokenExpiredException $e) { // 捕获token过期
                //刷新token
                $token = $this->auth->refresh();

                //携带payload
                $payload = $this->auth->auth(false);
                $request->payload = $payload['data']->getValue();

                $response = $next($request);

                //将新token放进header中返回给前端
                return $this->setAuthentication($response, $token);

            } catch (TokenBlacklistGracePeriodException $e) { // 捕获黑名单宽限期
                //携带payload
                $payload = $this->auth->auth(false);
                $request->payload = $payload['data']->getValue();

                return $next($request);
            }

        }

        return $next($request);
    }
}
