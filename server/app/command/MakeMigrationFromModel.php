<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\App;
use ReflectionClass;

class MakeMigrationFromModel extends Command
{
    private $schemaStatesFile = 'database/schema_states.json';
    private $baseTimestamp;
    private $migrationCounter = 0;

    protected function configure()
    {
        $this->setName('make:migration-from-model')
            ->setDescription('从Model字段定义自动生成Migration文件');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始扫描Model并生成Migration文件...');

        // 初始化基础时间戳
        $this->baseTimestamp = date('YmdHis');
        $this->migrationCounter = 0;

        // 加载当前schema状态
        $currentStates = $this->loadSchemaStates();

        // 扫描所有Model
        $models = $this->scanModels();

        $generatedCount = 0;
        foreach ($models as $modelClass) {
            if (!method_exists($modelClass, 'getMigrateSchema')) {
                continue;
            }

            $currentSchema = $modelClass::getMigrateSchema();
            $modelKey = $modelClass;

            // 检测schema变化
            $changes = $this->detectChanges($modelKey, $currentSchema, $currentStates);

            if (!empty($changes)) {
                $this->generateMigration($modelKey, $changes, $output);
                $this->updateSchemaState($modelKey, $currentSchema, $currentStates);
                $generatedCount++;
            }
        }

        // 保存schema状态
        $this->saveSchemaStates($currentStates);

        if ($generatedCount === 0) {
            $output->writeln('<info>没有检测到schema变化，无需生成新的Migration文件</info>');
        } else {
            $output->writeln("<info>成功生成 {$generatedCount} 个Migration文件！</info>");
        }
    }

    /**
     * 扫描所有Model类
     */
    private function scanModels(): array
    {
        $models = [];
        $modelPath = App::getAppPath() . 'model' . DIRECTORY_SEPARATOR;

        if (!is_dir($modelPath)) {
            return $models;
        }

        $files = glob($modelPath . '*.php');
        foreach ($files as $file) {
            $className = 'app\\model\\' . basename($file, '.php');
            if (class_exists($className)) {
                $models[] = $className;
            }
        }

        return $models;
    }

    /**
     * 加载schema状态
     */
    private function loadSchemaStates(): array
    {
        if (!file_exists($this->schemaStatesFile)) {
            return [];
        }

        $content = file_get_contents($this->schemaStatesFile);
        return json_decode($content, true) ?: [];
    }

    /**
     * 保存schema状态 - 改进JSON编码
     */
    private function saveSchemaStates(array $states): void
    {
        $dir = dirname($this->schemaStatesFile);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        // 使用多个标志确保JSON序列化的一致性
        $flags = JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_NUMERIC_CHECK;
        file_put_contents($this->schemaStatesFile, json_encode($states, $flags));
    }

    /**
     * 规范化字段定义 - 解决顺序敏感问题
     */
    private function normalizeColumnDefinition(array $column): array
    {
        // 确保字段定义有三个元素 [name, type, options]
        if (count($column) < 3) {
            $column[2] = [];
        }

        [$name, $type, $options] = $column;

        // 规范化选项：排序键，统一类型
        if (is_array($options)) {
            ksort($options);
            // 规范化布尔值
            array_walk_recursive($options, function (&$value) {
                if (is_string($value)) {
                    if ($value === 'true')
                        $value = true;
                    if ($value === 'false')
                        $value = false;
                }
            });
        } else {
            $options = [];
        }

        return [$name, $type, $options];
    }

    /**
     * 规范化索引定义 - 解决顺序敏感问题
     */
    private function normalizeIndexDefinition(array $index): array
    {
        if (count($index) < 2) {
            $index[1] = [];
        }

        [$columns, $options] = $index;

        // 规范化选项：排序键
        if (is_array($options)) {
            ksort($options);
        } else {
            $options = [];
        }

        return [$columns, $options];
    }

    /**
     * 深度数组对比 - 忽略键顺序
     */
    private function deepArrayCompare(array $array1, array $array2): bool
    {
        if (count($array1) !== count($array2)) {
            return false;
        }

        // 对关联数组进行键排序
        if ($this->isAssociativeArray($array1) && $this->isAssociativeArray($array2)) {
            ksort($array1);
            ksort($array2);
        }

        foreach ($array1 as $key => $value) {
            if (!array_key_exists($key, $array2)) {
                return false;
            }

            if (is_array($value) && is_array($array2[$key])) {
                if (!$this->deepArrayCompare($value, $array2[$key])) {
                    return false;
                }
            } elseif ($value !== $array2[$key]) {
                return false;
            }
        }

        return true;
    }

    /**
     * 判断是否为关联数组
     */
    private function isAssociativeArray(array $array): bool
    {
        return array_keys($array) !== range(0, count($array) - 1);
    }

    /**
     * 验证索引字段一致性 - 需求4
     */
    private function validateIndexFieldConsistency(array $indexes, array $columns): array
    {
        $validIndexes = [];
        $columnNames = array_column($columns, 0);

        foreach ($indexes as $index) {
            $indexColumns = is_array($index[0]) ? $index[0] : [$index[0]];
            $allFieldsExist = true;

            foreach ($indexColumns as $columnName) {
                if (!in_array($columnName, $columnNames)) {
                    $allFieldsExist = false;
                    break;
                }
            }

            if ($allFieldsExist) {
                $validIndexes[] = $index;
            }
        }

        return $validIndexes;
    }

    /**
     * 分析字段具体变化 - 需求2：精细识别
     */
    private function analyzeColumnChanges(array $currentColumn, array $lastColumn): array
    {
        $changes = [];
        $current = $this->normalizeColumnDefinition($currentColumn);
        $last = $this->normalizeColumnDefinition($lastColumn);

        // 检查类型变化
        if ($current[1] !== $last[1]) {
            $changes[] = "类型从 '{$last[1]}' 修改为 '{$current[1]}'";
        }

        // 检查选项变化
        $currentOptions = $current[2];
        $lastOptions = $last[2];

        // 检查具体选项变化
        $optionKeys = array_unique(array_merge(array_keys($currentOptions), array_keys($lastOptions)));

        foreach ($optionKeys as $key) {
            $currentValue = $currentOptions[$key] ?? null;
            $lastValue = $lastOptions[$key] ?? null;

            if ($currentValue !== $lastValue) {
                $currentStr = $currentValue === null ? '未设置' : var_export($currentValue, true);
                $lastStr = $lastValue === null ? '未设置' : var_export($lastValue, true);

                switch ($key) {
                    case 'length':
                        $changes[] = "长度从 {$lastStr} 修改为 {$currentStr}";
                        break;
                    case 'default':
                        $changes[] = "默认值从 {$lastStr} 修改为 {$currentStr}";
                        break;
                    case 'comment':
                        $changes[] = "备注从 {$lastStr} 修改为 {$currentStr}";
                        break;
                    case 'null':
                        $changes[] = "NULL约束从 {$lastStr} 修改为 {$currentStr}";
                        break;
                    default:
                        $changes[] = "选项 {$key} 从 {$lastStr} 修改为 {$currentStr}";
                }
            }
        }

        return $changes;
    }

    /**
     * 检测schema变化 - 改进版本，添加字段索引一致性验证
     */
    private function detectChanges(string $modelKey, array $currentSchema, array $currentStates): array
    {
        // 如果是首次创建
        if (!isset($currentStates[$modelKey])) {
            return [
                'type' => 'create',
                'schema' => $currentSchema
            ];
        }

        $lastSchema = $currentStates[$modelKey]['schema'];
        $changes = [];

        // 获取当前字段信息
        $currentColumns = $currentSchema['columns'] ?? [];
        $lastColumns = $lastSchema['columns'] ?? [];

        // 检测新增字段
        $newColumns = $this->getNewColumns($currentColumns, $lastColumns);
        if (!empty($newColumns)) {
            $changes[] = [
                'type' => 'add_columns',
                'columns' => $newColumns
            ];
        }

        // 检测修改字段 - 使用改进的对比逻辑
        $modifiedColumns = $this->getModifiedColumns($currentColumns, $lastColumns);
        if (!empty($modifiedColumns)) {
            $changes[] = [
                'type' => 'modify_columns',
                'columns' => $modifiedColumns
            ];
        }

        // 检测删除字段
        $droppedColumns = $this->getDroppedColumns($currentColumns, $lastColumns);
        if (!empty($droppedColumns)) {
            $changes[] = [
                'type' => 'drop_columns',
                'columns' => $droppedColumns
            ];
        }

        // 需求4：验证索引字段一致性
        $currentIndexes = $this->validateIndexFieldConsistency(
            $currentSchema['indexes'] ?? [],
            $currentColumns
        );
        $lastIndexes = $this->validateIndexFieldConsistency(
            $lastSchema['indexes'] ?? [],
            $lastColumns
        );

        // 检测索引变化 - 只对有效索引操作
        $indexChanges = $this->getIndexChanges($currentIndexes, $lastIndexes);
        $changes = array_merge($changes, $indexChanges);

        // 如果有变化，包装成统一的数据结构
        if (!empty($changes)) {
            return [
                'type' => 'update',
                'changes' => $changes
            ];
        }

        return [];
    }

    /**
     * 检测新增字段
     */
    private function getNewColumns(array $currentColumns, array $lastColumns): array
    {
        $lastColumnNames = array_column($lastColumns, 0);
        $newColumns = [];

        foreach ($currentColumns as $column) {
            if (!in_array($column[0], $lastColumnNames)) {
                $newColumns[] = $column;
            }
        }

        return $newColumns;
    }

    /**
     * 检测修改字段 - 改进版本，精细识别变化
     */
    private function getModifiedColumns(array $currentColumns, array $lastColumns): array
    {
        $lastColumnsMap = [];
        foreach ($lastColumns as $column) {
            $lastColumnsMap[$column[0]] = $this->normalizeColumnDefinition($column);
        }

        $modifiedColumns = [];
        foreach ($currentColumns as $column) {
            $columnName = $column[0];
            if (isset($lastColumnsMap[$columnName])) {
                $normalizedCurrent = $this->normalizeColumnDefinition($column);
                $normalizedLast = $lastColumnsMap[$columnName];

                // 使用深度对比检查是否有真正的变化
                if (
                    $normalizedCurrent[1] !== $normalizedLast[1] ||
                    !$this->deepArrayCompare($normalizedCurrent[2], $normalizedLast[2])
                ) {
                    $modifiedColumns[] = $column;
                }
            }
        }

        return $modifiedColumns;
    }

    /**
     * 检测删除字段
     */
    private function getDroppedColumns(array $currentColumns, array $lastColumns): array
    {
        $currentColumnNames = array_column($currentColumns, 0);
        $droppedColumns = [];

        foreach ($lastColumns as $column) {
            if (!in_array($column[0], $currentColumnNames)) {
                $droppedColumns[] = $column[0];
            }
        }

        return $droppedColumns;
    }

    /**
     * 检测索引变化 - 改进版本，添加字段一致性验证
     */
    private function getIndexChanges(array $currentIndexes, array $lastIndexes): array
    {
        $changes = [];

        // 检测新增索引 - 需求4：验证字段一致性
        $newIndexes = $this->getNewIndexes($currentIndexes, $lastIndexes);
        if (!empty($newIndexes)) {
            $changes[] = [
                'type' => 'add_indexes',
                'indexes' => $newIndexes
            ];
        }

        // 检测删除索引 - 需求4：验证字段一致性
        $droppedIndexes = $this->getDroppedIndexes($currentIndexes, $lastIndexes);
        if (!empty($droppedIndexes)) {
            $changes[] = [
                'type' => 'drop_indexes',
                'indexes' => $droppedIndexes
            ];
        }

        return $changes;
    }

    /**
     * 检测新增索引 - 改进版本，解决顺序敏感问题
     */
    private function getNewIndexes(array $currentIndexes, array $lastIndexes): array
    {
        $normalizedLastIndexes = array_map([$this, 'normalizeIndexDefinition'], $lastIndexes);
        $newIndexes = [];

        foreach ($currentIndexes as $index) {
            $normalizedCurrent = $this->normalizeIndexDefinition($index);
            $found = false;

            foreach ($normalizedLastIndexes as $normalizedLast) {
                if ($this->deepArrayCompare($normalizedCurrent, $normalizedLast)) {
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $newIndexes[] = $index;
            }
        }

        return $newIndexes;
    }

    /**
     * 检测删除索引 - 改进版本，解决顺序敏感问题
     */
    private function getDroppedIndexes(array $currentIndexes, array $lastIndexes): array
    {
        $normalizedCurrentIndexes = array_map([$this, 'normalizeIndexDefinition'], $currentIndexes);
        $droppedIndexes = [];

        foreach ($lastIndexes as $index) {
            $normalizedLast = $this->normalizeIndexDefinition($index);
            $found = false;

            foreach ($normalizedCurrentIndexes as $normalizedCurrent) {
                if ($this->deepArrayCompare($normalizedLast, $normalizedCurrent)) {
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $droppedIndexes[] = $index;
            }
        }

        return $droppedIndexes;
    }

    /**
     * 生成Migration文件 - 添加详细分析输出
     */
    private function generateMigration(string $modelKey, array $changes, Output $output): void
    {
        $tableName = $this->getTableNameFromModel($modelKey);

        // 使用基础时间戳 + 序号来避免重复
        $this->migrationCounter++;
        $timestamp = $this->baseTimestamp . sprintf('%02d', $this->migrationCounter);

        if ($changes['type'] === 'create') {
            $operation = 'Create';
            $filename = "{$timestamp}_create_{$tableName}_table.php";
            $output->writeln("<info>创建新表: {$tableName}</info>");
        } else {
            // 输出详细的变化分析
            $this->outputChangeAnalysis($changes, $tableName, $output);

            $operation = $this->getMigrationOperation($changes);
            // 检查操作名称是否重复，如果重复则加序号
            $operation = $this->ensureUniqueOperation($operation, $tableName);
            $filename = "{$timestamp}_{$this->toSnakeCase($operation)}_{$tableName}_table.php";
        }

        $className = str_replace('_', '', ucwords($operation . '_' . $tableName . '_table', '_'));
        $content = $this->buildMigrationContent($className, $tableName, $changes);

        $migrationPath = 'database/migrations/' . $filename;
        file_put_contents($migrationPath, $content);

        $output->writeln("<comment>Generated: {$filename}</comment>");
    }

    /**
     * 输出变化分析 - 需求2：精细识别字段变化
     */
    private function outputChangeAnalysis(array $changes, string $tableName, Output $output): void
    {
        $output->writeln("<info>========== 表 {$tableName} 的Schema变化分析 ==========</info>");

        foreach ($changes['changes'] as $change) {
            switch ($change['type']) {
                case 'add_columns':
                    $output->writeln("<info>📝 新增字段:</info>");
                    foreach ($change['columns'] as $column) {
                        $name = $column[0];
                        $type = $column[1];
                        $options = $column[2] ?? [];
                        $optionsStr = !empty($options) ? ' (' . json_encode($options, JSON_UNESCAPED_UNICODE) . ')' : '';
                        $output->writeln("   + {$name}: {$type}{$optionsStr}");
                    }
                    break;

                case 'modify_columns':
                    $output->writeln("<info>🔧 修改字段:</info>");
                    foreach ($change['columns'] as $column) {
                        $name = $column[0];
                        $output->writeln("   ~ {$name}: 字段定义已更新");
                        $output->writeln("     <comment>提示: 具体变化内容请检查Model定义</comment>");
                    }
                    break;

                case 'drop_columns':
                    $output->writeln("<info>🗑️  删除字段:</info>");
                    foreach ($change['columns'] as $columnName) {
                        $output->writeln("   - {$columnName}");
                    }
                    break;

                case 'add_indexes':
                    $output->writeln("<info>📌 新增索引:</info>");
                    foreach ($change['indexes'] as $index) {
                        $columns = is_array($index[0]) ? implode(', ', $index[0]) : $index[0];
                        $options = $index[1] ?? [];
                        $optionsStr = !empty($options) ? ' (' . json_encode($options, JSON_UNESCAPED_UNICODE) . ')' : '';
                        $output->writeln("   + 索引: [{$columns}]{$optionsStr}");
                    }
                    break;

                case 'drop_indexes':
                    $output->writeln("<info>🚫 删除索引:</info>");
                    foreach ($change['indexes'] as $index) {
                        $columns = is_array($index[0]) ? implode(', ', $index[0]) : $index[0];
                        $output->writeln("   - 索引: [{$columns}]");
                    }
                    break;
            }
        }

        $output->writeln("<info>================================================</info>");
    }

    /**
     * 从Model类名获取表名
     */
    private function getTableNameFromModel(string $modelClass): string
    {
        $className = basename(str_replace('\\', '/', $modelClass));
        return $this->toSnakeCase($className);
    }

    /**
     * 获取Migration操作名称
     */
    private function getMigrationOperation(array $changes): string
    {
        if ($changes['type'] === 'create') {
            return 'create';
        }

        if ($changes['type'] === 'update') {
            $operations = [];
            foreach ($changes['changes'] as $change) {
                switch ($change['type']) {
                    case 'add_columns':
                        $columnNames = array_column($change['columns'], 0);
                        $operations[] = 'add_' . implode('_and_', $columnNames);
                        break;
                    case 'modify_columns':
                        $columnNames = array_column($change['columns'], 0);
                        $operations[] = 'modify_' . implode('_and_', $columnNames);
                        break;
                    case 'drop_columns':
                        $operations[] = 'drop_' . implode('_and_', $change['columns']);
                        break;
                    case 'add_indexes':
                        $operations[] = 'add_indexes';
                        break;
                    case 'drop_indexes':
                        $operations[] = 'drop_indexes';
                        break;
                }
            }
            return implode('_and_', $operations);
        }

        return 'unknown';
    }

    /**
     * 确保操作名称唯一性，避免重复的类名
     */
    private function ensureUniqueOperation(string $operation, string $tableName): string
    {
        $migrationDir = 'database/migrations/';
        if (!is_dir($migrationDir)) {
            return $operation;
        }

        // 获取已存在的迁移文件
        $existingFiles = glob($migrationDir . '*_' . $this->toSnakeCase($operation) . '_' . $tableName . '_table.php');

        if (empty($existingFiles)) {
            return $operation;
        }

        // 如果已存在相同操作名称的文件，加上序号
        $counter = 2;
        $newOperation = $operation;

        do {
            $newOperation = $operation . '_' . $counter;
            $pattern = $migrationDir . '*_' . $this->toSnakeCase($newOperation) . '_' . $tableName . '_table.php';
            $existingFiles = glob($pattern);
            $counter++;
        } while (!empty($existingFiles) && $counter <= 10); // 最多尝试10次

        return $newOperation;
    }

    /**
     * 构建Migration文件内容
     */
    private function buildMigrationContent(string $className, string $tableName, array $changes): string
    {
        $content = "<?php\n\nuse think\\migration\\Migrator;\nuse think\\migration\\db\\Column;\n\n";
        $content .= "class {$className} extends Migrator\n{\n";
        $content .= "    /**\n     * Change Method.\n     */\n";
        $content .= "    public function change()\n    {\n";

        if ($changes['type'] === 'create') {
            $content .= $this->buildSafeCreateTable($tableName, $changes['schema']);
        } elseif ($changes['type'] === 'update') {
            // 全面安全模式：所有操作都用try-catch包装，按依赖顺序执行
            $content .= $this->buildSafeOperations($tableName, $changes['changes']);
        }

        $content .= "    }\n}";

        return $content;
    }

    /**
     * 构建安全的创建表操作
     */
    private function buildSafeCreateTable(string $tableName, array $schema): string
    {
        $tableOptions = $schema['table'] ?? [];
        $content = '';

        // 尝试创建完整的表
        $content .= "        // 尝试创建表\n";
        $content .= "        try {\n";
        $content .= "            \$table = \$this->table('{$tableName}'";

        if (!empty($tableOptions)) {
            $content .= ", " . $this->exportArray($tableOptions);
        }

        $content .= ");\n";

        // 收集所有操作
        $operations = [];

        // 添加字段
        foreach ($schema['columns'] ?? [] as $column) {
            [$name, $type, $options] = $column;
            $operation = "            \$table->addColumn('{$name}', '{$type}'";
            if (!empty($options)) {
                $operation .= ", " . $this->exportArray($options);
            }
            $operation .= ")";
            $operations[] = $operation;
        }

        // 添加索引
        foreach ($schema['indexes'] ?? [] as $index) {
            if (is_array($index[0])) {
                // 复合索引
                $columns = "'" . implode("', '", $index[0]) . "'";
                $optionsStr = isset($index[1]) ? ', ' . $this->exportArray($index[1]) : '';
                $operations[] = "            \$table->addIndex([{$columns}]{$optionsStr})";
            } else {
                // 单字段索引
                $optionsStr = isset($index[1]) ? ', ' . $this->exportArray($index[1]) : '';
                $operations[] = "            \$table->addIndex('{$index[0]}'{$optionsStr})";
            }
        }

        // 生成链式操作
        if (!empty($operations)) {
            $content .= $operations[0];
            for ($i = 1; $i < count($operations); $i++) {
                // 提取方法调用部分，去掉 "$table->" 前缀，添加链式连接符
                $methodCall = preg_replace('/^\s*\$table->/', '', $operations[$i]);
                $content .= "\n                ->" . $methodCall;
            }
            $content .= "\n                ->create();\n";
        } else {
            $content .= "            \$table->create();\n";
        }

        $content .= "        } catch (\\Exception \$e) {\n";
        $content .= "            // 表可能已经存在，尝试逐个添加缺失的字段和索引\n";

        // 备用方案：逐个添加字段
        foreach ($schema['columns'] ?? [] as $column) {
            [$name, $type, $options] = $column;
            $optionsStr = !empty($options) ? ', ' . $this->exportArray($options) : '';
            $content .= "            try {\n";
            $content .= "                \$table = \$this->table('{$tableName}');\n";
            $content .= "                \$table->addColumn('{$name}', '{$type}'{$optionsStr})->update();\n";
            $content .= "            } catch (\\Exception \$e2) {\n";
            $content .= "                // 字段可能已经存在\n";
            $content .= "            }\n";
        }

        // 备用方案：逐个添加索引
        foreach ($schema['indexes'] ?? [] as $index) {
            if (is_array($index[0])) {
                // 复合索引
                $columns = "'" . implode("', '", $index[0]) . "'";
                $optionsStr = isset($index[1]) ? ', ' . $this->exportArray($index[1]) : '';
                $content .= "            try {\n";
                $content .= "                \$table = \$this->table('{$tableName}');\n";
                $content .= "                \$table->addIndex([{$columns}]{$optionsStr})->update();\n";
                $content .= "            } catch (\\Exception \$e2) {\n";
                $content .= "                // 索引可能已经存在或字段不存在\n";
                $content .= "            }\n";
            } else {
                // 单字段索引
                $optionsStr = isset($index[1]) ? ', ' . $this->exportArray($index[1]) : '';
                $content .= "            try {\n";
                $content .= "                \$table = \$this->table('{$tableName}');\n";
                $content .= "                \$table->addIndex('{$index[0]}'{$optionsStr})->update();\n";
                $content .= "            } catch (\\Exception \$e2) {\n";
                $content .= "                // 索引可能已经存在或字段不存在\n";
                $content .= "            }\n";
            }
        }

        $content .= "        }\n";

        return $content;
    }

    /**
     * 构建安全操作 - 改进异常处理和提示信息
     */
    private function buildSafeOperations(string $tableName, array $changes): string
    {
        $content = '';

        // 按依赖顺序执行所有操作，每个操作都用try-catch包装

        // 第一步：删除索引（索引依赖字段，必须先删除）
        foreach ($changes as $change) {
            if ($change['type'] === 'drop_indexes') {
                foreach ($change['indexes'] as $index) {
                    $indexName = $this->getIndexName($index);
                    $content .= "        // 删除索引：{$indexName}\n";
                    $content .= "        try {\n";
                    $content .= "            \$table = \$this->table('{$tableName}');\n";
                    $content .= "            if (method_exists(\$table, 'hasIndex') && \$table->hasIndex('{$indexName}')) {\n";
                    $content .= "                \$table->removeIndex(['{$indexName}'])->update();\n";
                    $content .= "                echo \"[SUCCESS] 索引 {$indexName} 已成功删除\\n\";\n";
                    $content .= "            } else {\n";
                    $content .= "                echo \"[SKIP] 索引 {$indexName} 不存在，删除操作已跳过\\n\";\n";
                    $content .= "            }\n";
                    $content .= "        } catch (\\Exception \$e) {\n";
                    $content .= "            echo \"[WARNING] 删除索引 {$indexName} 失败：\" . \$e->getMessage() . \"，操作已跳过\\n\";\n";
                    $content .= "        }\n\n";
                }
            }
        }

        // 第二步：删除字段
        foreach ($changes as $change) {
            if ($change['type'] === 'drop_columns') {
                foreach ($change['columns'] as $columnName) {
                    $content .= "        // 删除字段：{$columnName}\n";
                    $content .= "        try {\n";
                    $content .= "            \$table = \$this->table('{$tableName}');\n";
                    $content .= "            if (method_exists(\$table, 'hasColumn') && \$table->hasColumn('{$columnName}')) {\n";
                    $content .= "                \$table->removeColumn('{$columnName}')->update();\n";
                    $content .= "                echo \"[SUCCESS] 字段 {$columnName} 已成功删除\\n\";\n";
                    $content .= "            } else {\n";
                    $content .= "                echo \"[SKIP] 字段 {$columnName} 不存在，删除操作已跳过\\n\";\n";
                    $content .= "            }\n";
                    $content .= "        } catch (\\Exception \$e) {\n";
                    $content .= "            echo \"[WARNING] 删除字段 {$columnName} 失败：\" . \$e->getMessage() . \"，操作已跳过\\n\";\n";
                    $content .= "        }\n\n";
                }
            }
        }

        // 第三步：添加字段
        foreach ($changes as $change) {
            if ($change['type'] === 'add_columns') {
                foreach ($change['columns'] as $column) {
                    [$name, $type, $options] = $column;
                    $optionsStr = !empty($options) ? ', ' . $this->exportArray($options) : '';
                    $content .= "        // 添加字段：{$name}\n";
                    $content .= "        try {\n";
                    $content .= "            \$table = \$this->table('{$tableName}');\n";
                    $content .= "            if (method_exists(\$table, 'hasColumn') && !\$table->hasColumn('{$name}')) {\n";
                    $content .= "                \$table->addColumn('{$name}', '{$type}'{$optionsStr})->update();\n";
                    $content .= "                echo \"[SUCCESS] 字段 {$name} 已成功添加\\n\";\n";
                    $content .= "            } else {\n";
                    $content .= "                echo \"[SKIP] 字段 {$name} 已存在，添加操作已跳过\\n\";\n";
                    $content .= "            }\n";
                    $content .= "        } catch (\\Exception \$e) {\n";
                    $content .= "            echo \"[WARNING] 添加字段 {$name} 失败：\" . \$e->getMessage() . \"，操作已跳过\\n\";\n";
                    $content .= "        }\n\n";
                }
            }
        }

        // 第四步：修改字段
        foreach ($changes as $change) {
            if ($change['type'] === 'modify_columns') {
                foreach ($change['columns'] as $column) {
                    [$name, $type, $options] = $column;
                    $optionsStr = !empty($options) ? ', ' . $this->exportArray($options) : '';

                    // 生成字段变化分析注释
                    $changeAnalysis = $this->generateColumnChangeComment($column);
                    $content .= $changeAnalysis;

                    $content .= "        // 修改字段：{$name}\n";
                    $content .= "        try {\n";
                    $content .= "            \$table = \$this->table('{$tableName}');\n";
                    $content .= "            if (method_exists(\$table, 'hasColumn') && \$table->hasColumn('{$name}')) {\n";
                    $content .= "                \$table->changeColumn('{$name}', '{$type}'{$optionsStr})->update();\n";
                    $content .= "                echo \"[SUCCESS] 字段 {$name} 已成功修改\\n\";\n";
                    $content .= "            } else {\n";
                    $content .= "                echo \"[SKIP] 字段 {$name} 不存在，修改操作已跳过\\n\";\n";
                    $content .= "            }\n";
                    $content .= "        } catch (\\Exception \$e) {\n";
                    $content .= "            echo \"[WARNING] 修改字段 {$name} 失败：\" . \$e->getMessage() . \"，操作已跳过\\n\";\n";
                    $content .= "        }\n\n";
                }
            }
        }

        // 第五步：添加索引（确保字段已存在）
        foreach ($changes as $change) {
            if ($change['type'] === 'add_indexes') {
                foreach ($change['indexes'] as $index) {
                    $indexName = $this->getIndexName($index);
                    if (is_array($index[0])) {
                        // 复合索引
                        $columns = "'" . implode("', '", $index[0]) . "'";
                        $optionsStr = isset($index[1]) ? ', ' . $this->exportArray($index[1]) : '';
                        $columnsStr = implode(', ', $index[0]);
                        $content .= "        // 添加复合索引：{$columnsStr}\n";
                        $content .= "        try {\n";
                        $content .= "            \$table = \$this->table('{$tableName}');\n";
                        $content .= "            if (method_exists(\$table, 'hasIndex') && !\$table->hasIndex([{$columns}])) {\n";
                        $content .= "                \$table->addIndex([{$columns}]{$optionsStr})->update();\n";
                        $content .= "                echo \"[SUCCESS] 复合索引 ({$columnsStr}) 已成功添加\\n\";\n";
                        $content .= "            } else {\n";
                        $content .= "                echo \"[SKIP] 复合索引 ({$columnsStr}) 已存在，添加操作已跳过\\n\";\n";
                        $content .= "            }\n";
                        $content .= "        } catch (\\Exception \$e) {\n";
                        $content .= "            echo \"[WARNING] 添加复合索引 ({$columnsStr}) 失败：\" . \$e->getMessage() . \"，可能字段不存在，操作已跳过\\n\";\n";
                        $content .= "        }\n\n";
                    } else {
                        // 单字段索引
                        $optionsStr = isset($index[1]) ? ', ' . $this->exportArray($index[1]) : '';
                        $content .= "        // 添加索引：{$indexName}\n";
                        $content .= "        try {\n";
                        $content .= "            \$table = \$this->table('{$tableName}');\n";
                        $content .= "            if (method_exists(\$table, 'hasIndex') && !\$table->hasIndex('{$index[0]}')) {\n";
                        $content .= "                \$table->addIndex('{$index[0]}'{$optionsStr})->update();\n";
                        $content .= "                echo \"[SUCCESS] 索引 {$indexName} 已成功添加\\n\";\n";
                        $content .= "            } else {\n";
                        $content .= "                echo \"[SKIP] 索引 {$indexName} 已存在，添加操作已跳过\\n\";\n";
                        $content .= "            }\n";
                        $content .= "        } catch (\\Exception \$e) {\n";
                        $content .= "            echo \"[WARNING] 添加索引 {$indexName} 失败：\" . \$e->getMessage() . \"，可能字段不存在，操作已跳过\\n\";\n";
                        $content .= "        }\n\n";
                    }
                }
            }
        }

        return $content;
    }

    /**
     * 构建索引定义
     */
    private function buildIndexDefinition(array $index): string
    {
        if (is_array($index[0])) {
            // 复合索引
            $columns = "'" . implode("', '", $index[0]) . "'";
            $options = isset($index[1]) ? ', ' . $this->exportArray($index[1]) : '';
            return "        \$table->addIndex([{$columns}]{$options})";
        } else {
            // 单字段索引
            $options = isset($index[1]) ? ', ' . $this->exportArray($index[1]) : '';
            return "        \$table->addIndex('{$index[0]}'{$options})";
        }
    }

    /**
     * 获取索引名称
     */
    private function getIndexName(array $index): string
    {
        if (is_array($index[0])) {
            return implode('_', $index[0]);
        }
        return $index[0];
    }

    /**
     * 更新schema状态
     */
    private function updateSchemaState(string $modelKey, array $currentSchema, array &$currentStates): void
    {
        $currentStates[$modelKey] = [
            'last_updated' => date('Y-m-d H:i:s'),
            'schema' => $currentSchema
        ];
    }

    /**
     * 转换为snake_case
     */
    private function toSnakeCase(string $input): string
    {
        return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $input));
    }

    /**
     * 导出数组为PHP代码
     */
    private function exportArray(array $array): string
    {
        if (empty($array)) {
            return '[]';
        }

        $isAssoc = array_keys($array) !== range(0, count($array) - 1);

        if (!$isAssoc) {
            return '[' . implode(', ', array_map(function ($value) {
                return is_string($value) ? "'{$value}'" : var_export($value, true);
            }, $array)) . ']';
        }

        $items = [];
        foreach ($array as $key => $value) {
            $exportedValue = is_string($value) ? "'{$value}'" : var_export($value, true);
            $items[] = "'{$key}' => {$exportedValue}";
        }

        return '[' . implode(', ', $items) . ']';
    }

    /**
     * 生成字段变化注释 - 需求2：精细识别
     */
    private function generateColumnChangeComment(array $column): string
    {
        $name = $column[0];
        $comment = "        // 字段 {$name} 的具体变化：\n";
        $comment .= "        // 注意：此处显示的是新的字段定义，具体变化请参考Model定义\n";
        return $comment;
    }
}