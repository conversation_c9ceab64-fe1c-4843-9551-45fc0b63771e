# Model 字段定义自动生成 Migration 功能

## 功能概述

这个功能允许你在 Model 类中定义字段信息，然后通过自定义命令自动识别并生成对应的 migrate 文件。支持智能识别 create、update、add、modify、drop 等操作类型。

## 核心特性

✅ **零学习成本** - 直接使用 think-migrate 原生语法  
✅ **智能识别** - 自动判断 create/update 操作类型  
✅ **增量更新** - 支持字段增删改，索引变更  
✅ **防重复生成** - 通过状态追踪避免重复生成  
✅ **完整历史** - 生成标准的 migration 历史文件

## 使用方法

### 1. 在 Model 中定义字段

```php
<?php
namespace app\model;

use think\Model;

class User extends Model
{
    /**
     * 数据表字段定义 - 使用think-migrate原生语法
     */
    protected static $migrateSchema = [
        // 表配置
        'table' => [
            'engine' => 'InnoDB',
            'comment' => '用户表'
        ],

        // 字段定义 - 完全按照think-migrate的addColumn语法
        'columns' => [
            ['username', 'string', ['limit' => 25, 'null' => false, 'default' => '', 'comment' => '用户名']],
            ['email', 'string', ['limit' => 100, 'null' => false, 'comment' => '邮箱']],
            ['password', 'string', ['limit' => 60, 'null' => false, 'comment' => '密码']],
            ['status', 'integer', ['default' => 1, 'comment' => '状态']],
            ['create_time', 'datetime', ['default' => null, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'comment' => '更新时间']],
        ],

        // 索引定义
        'indexes' => [
            ['username', ['unique' => true]],  // 唯一索引
            ['email', ['unique' => true]],     // 唯一索引
            [['status', 'create_time']],       // 复合索引
        ],
    ];
}
```

### 2. 运行命令生成 Migration

```bash
# 生成migration文件
php think make:migration-from-model
```

### 3. 执行 Migration

```bash
# 执行数据库迁移
php think migrate:run
```

## 支持的字段类型

支持 think-migrate 的所有字段类型：

- `string` - 字符串类型
- `text` - 文本类型
- `integer` - 整数类型
- `boolean` - 布尔类型
- `datetime` - 日期时间类型
- `decimal` - 小数类型
- `json` - JSON 类型
- 等等...

## 支持的字段选项

支持 think-migrate 的所有字段选项：

- `limit` - 字段长度
- `null` - 是否允许 NULL
- `default` - 默认值
- `comment` - 字段注释
- `unique` - 唯一约束
- `after` - 在某字段后添加
- 等等...

## 使用场景演示

### 场景 1：首次创建表

```php
// 新建Model，定义字段
class Product extends Model
{
    protected static $migrateSchema = [
        'table' => ['engine' => 'InnoDB', 'comment' => '商品表'],
        'columns' => [
            ['name', 'string', ['limit' => 100, 'null' => false, 'comment' => '商品名称']],
            ['price', 'decimal', ['precision' => 10, 'scale' => 2, 'comment' => '价格']],
        ],
    ];
}

// 运行命令 → 生成: 20250109120000_create_product_table.php
```

### 场景 2：添加新字段

```php
// 在现有Model中添加字段
protected static $migrateSchema = [
    'columns' => [
        ['name', 'string', ['limit' => 100, 'null' => false, 'comment' => '商品名称']],
        ['price', 'decimal', ['precision' => 10, 'scale' => 2, 'comment' => '价格']],
        ['description', 'text', ['comment' => '商品描述']], // 新增字段
    ],
];

// 运行命令 → 生成: 20250109130000_add_description_to_product_table.php
```

### 场景 3：修改字段

```php
// 修改字段定义
protected static $migrateSchema = [
    'columns' => [
        ['name', 'string', ['limit' => 200, 'null' => false, 'comment' => '商品名称']], // 长度从100改为200
        ['price', 'decimal', ['precision' => 10, 'scale' => 2, 'comment' => '价格']],
        ['description', 'text', ['comment' => '商品描述']],
    ],
];

// 运行命令 → 生成: 20250109140000_modify_name_to_product_table.php
```

### 场景 4：删除字段

```php
// 删除字段
protected static $migrateSchema = [
    'columns' => [
        ['name', 'string', ['limit' => 200, 'null' => false, 'comment' => '商品名称']],
        ['price', 'decimal', ['precision' => 10, 'scale' => 2, 'comment' => '价格']],
        // description字段被删除
    ],
];

// 运行命令 → 生成: 20250109150000_drop_description_to_product_table.php
```

### 场景 5：索引变更

```php
// 添加/修改索引
protected static $migrateSchema = [
    'columns' => [...],
    'indexes' => [
        ['name'],  // 新增普通索引
        ['price', ['unique' => true]],  // 新增唯一索引
        [['name', 'price']],  // 新增复合索引
    ],
];

// 运行命令 → 生成: 20250109160000_add_indexes_to_product_table.php
```

## 状态追踪机制

系统会在 `database/schema_states.json` 文件中记录每个 Model 的 schema 状态：

```json
{
    "app\\model\\User": {
        "last_updated": "2025-01-09 12:00:00",
        "schema": {
            "table": {"engine": "InnoDB", "comment": "用户表"},
            "columns": [...],
            "indexes": [...]
        }
    }
}
```

## 注意事项

1. **首次运行** - 会为所有定义了 `$migrateSchema` 的 Model 生成 create 类型 migration
2. **重复运行** - 只会为有变化的 Model 生成新的 migration，避免重复
3. **字段删除** - 删除字段会生成 drop 操作，请谨慎操作生产环境
4. **版本控制** - `schema_states.json` 文件应该加入版本控制，保证团队同步

## 生成的 Migration 文件示例

### Create 类型

```php
<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateUserTable extends Migrator
{
    public function change()
    {
        $table = $this->table('user', ['engine' => 'InnoDB', 'comment' => '用户表']);
        $table->addColumn('username', 'string', ['limit' => 25, 'null' => false, 'default' => '', 'comment' => '用户名'])
            ->addColumn('email', 'string', ['limit' => 100, 'null' => false, 'comment' => '邮箱'])
            ->addIndex('username', ['unique' => true])
            ->addIndex('email', ['unique' => true])
            ->create();
    }
}
```

### Update 类型

```php
<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddPhoneToUserTable extends Migrator
{
    public function change()
    {
        $table = $this->table('user');
        $table->addColumn('phone', 'string', ['limit' => 20, 'comment' => '手机号'])
            ->update();
    }
}
```

## 命令参数

```bash
# 基本用法
php think make:migration-from-model

# 查看帮助
php think make:migration-from-model --help
```

这个功能完美解决了手动编写 migration 的繁琐问题，让你专注于 Model 字段定义，自动化处理 migration 生成！
