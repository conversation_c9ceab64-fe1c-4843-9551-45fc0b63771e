<?php
// 应用公共文件

//成功时返回数据
function success($msg = '', $data = [])
{
    return json([
        'code' => 0,
        'message' => $msg,
        'data' => $data,
    ]);
}

//返回异常
function error($msg, $code = 1)
{
    throw new \app\exception\BaseException($code, $msg);
}

//生成UUID
function get_uuid(){
    $data = random_bytes(16);
    $data[6] = chr((ord($data[6]) & 0x0f) | 0x40); // 设置版本号为 0100
    $data[8] = chr((ord($data[8]) & 0x3f) | 0x80); // 设置 variant 为 10
    return sprintf(
        '%08s-%04s-%04s-%04s-%12s',
        bin2hex(substr($data, 0, 4)),
        bin2hex(substr($data, 4, 2)),
        bin2hex(substr($data, 6, 2)),
        bin2hex(substr($data, 8, 2)),
        bin2hex(substr($data, 10, 6))
    );
}

//发送OPC数据
function OPC_Send($data){

    $host = env('WIN.HOST','127.0.0.1');
    $port = env('WIN.PORT','8082');
    $url = "http://{$host}:{$port}/restfulApi/write";
    $opt_data = json_encode($data);
    $header = array();
    $header[] = 'Accept:*/*';
    $header[] = 'Accept:application/json';
    $header[] = 'Cache-Control:no-cache';
    $header[] = 'Content-Type:application/json';
    $curl = curl_init();  //初始化
    curl_setopt($curl,CURLOPT_URL,$url);                  //设置url
    curl_setopt($curl,CURLOPT_HTTPHEADER,$header);        //设置header
    curl_setopt($curl,CURLOPT_POST,1);              //post提交方式
    curl_setopt($curl,CURLOPT_POSTFIELDS,$opt_data);      //参数
    curl_setopt($curl,CURLOPT_RETURNTRANSFER,1);    //设置获取的信息以文件流的形式返回，而不是直接输出。
    curl_setopt($curl,CURLOPT_TIMEOUT,1);           //设置超时时间
    $data = curl_exec($curl);                   //运行curl
    $data1 = str_replace("true","\"True\"",$data);
    $data2 = str_replace("false","\"False\"",$data1);
    $data3 = str_replace("null","\"None\"",$data2);
//        echo $data3."\n";
    $obj = json_decode($data3,true);
    curl_close($curl);
    return $obj;
}
