<?php
/**
 * User: <PERSON><PERSON>
 * Date: 2022/4/15
 * Time: 23:13
 */

namespace app\controller;

use app\WMS;
use app\extend\Captcha;
use app\service\User as UserService;
use thans\jwt\facade\JWTAuth;

class User extends WMS
{
    //获取验证码
    public function captcha()
    {
        $captcha = new Captcha();
        return success('',[
            'uuid' => $captcha->getUUid(),
            'base64' => 'data:image/png;base64,'.$captcha->getBase64Data(),
        ]);
    }

    //登陆
    public function login()
    {
        $param = $this->request->param();

        //验证
        $this->validate($param, [
            'username|用户名' => 'require',
            'password|密码' => 'require',
            'uuid' => 'require',
            'code|验证码' => 'require',
        ]);

        $userInfo = UserService::login($param['username'], $param['password'], $param['uuid'], $param['code']);

        return success('登陆成功',[
            'access_token' => 'Bearer ' . JWTAuth::builder([
                'data' => $userInfo
            ]),
        ]);
    }
}
