<?php
/**
 * User: <PERSON><PERSON>
 * Date: 2021/9/24
 * Time: 15:23
 */

namespace app\exception;

use think\Exception;

class BaseException extends Exception
{
    /**
     * 特殊业务异常的状态码
     */
    private $errorCode = [
        500 => '系统错误',
        401 => '登录失效，请重新登录',
        403 => '操作失败，此操作未经授权',
        404 => '接口地址不存在'
    ];

    public function __construct($code, $msg = '')
    {
        $errMsg = key_exists($code,$this->errorCode) ? $this->errorCode[$code] : $msg;
        $this->code = $code;
        $this->message = $errMsg;
    }
}
