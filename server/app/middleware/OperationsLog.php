<?php
/**
 * User: <PERSON><PERSON>
 * Date: 2024/12/2
 * Time: 17:22
 */

namespace app\middleware;


use app\model\Menu;
use app\model\UserLog;
use think\facade\Config;

class OperationsLog
{
    public function handle($request, \Closure $next)
    {
        $ip = $request->ip();
        $url = $request->url();
        $userId = $request->payload ? $request->payload->id : 0;
        $nickname = $request->payload ? $request->payload->nickname : null;
        $path = '/'.$request->pathinfo();

        $noLoginApi = Config::get('global.no_login');
        $publicApi = Config::get('global.public_api');

        if (!in_array($path, array_merge($noLoginApi, $publicApi, ['/UserLog/tableList']))) {
            $menu = Menu::where('api_path', $path)->find();
            $parentMenu = Menu::find($menu->parent_id);
            // UserLog::create(['ip' => $ip, 'module' => $parentMenu ? $parentMenu->title : '未知', 'action' => $menu ? $menu->title : '未知', 'path' => $url, 'user_id' => $userId, 'nickname' => $nickname]);
        }

        return $next($request);
    }
}
