<?php
/**
 * User: <PERSON><PERSON>
 * Date: 2022/4/17
 * Time: 19:21
 */

namespace app\model;


use think\Model;

class BaseModel extends Model
{

    /**
     * 定义前后端字段转换
     */
    protected static $keys = [
        'createTime' => 'create_time',
    ];

    /**
     * 设置字段
     */
    protected static function setKeys($keys)
    {
        //注意：不要设置id字段的转换，因为每个表都会用到id，重复的key或者value会造不必要的麻烦。
        if (in_array('id', $keys)) error('id不可作为转换字段');
        self::$keys = array_merge(self::$keys, $keys);
    }

    /**
     * 转换字段名称 (将前端参数名称转换成后端表字段名称)
     */
    public static function transKey($data, $append = [])
    {
        if ($append) self::$keys = array_merge(self::$keys, $append);

        $keys = array_keys($data);
        $values = array_values($data);
        foreach ($keys as $k => $key)
        {
            if (key_exists($key, self::$keys))
            {
                $keys[$k] = self::$keys[$key];
            }
        }

        //递归处理多维数组
        foreach ($values as $k => $v)
        {
            if (is_array($v))
            {
                $arr = self::transKey($v);
                $values[$k] = $arr;
            }
        }

        return array_combine($keys,$values);
    }

    /**
     * 转换字段名称 (将后端表字段名称转换成前端参数名称)
     */
    public static function unTransKey($data, $append = [])
    {
        if ($append) self::$keys = array_merge(self::$keys, $append);

        foreach ($data as $k => $row)
        {
            $row = is_array($row) ? $row : json_decode($row,true);
            $keys = array_keys($row);
            $values = array_values($row);

            foreach ($keys as $k2 => $key)
            {
                if (in_array($key, self::$keys)) {
                    $key_name = array_search($key, self::$keys);
                    $keys[$k2] = $key_name;
                }
            }

            foreach ($values as $k2 => $v)
            {
                if (is_array($v)) {
                    $arr = self::unTransKey($v);
                    $values[$k2] = $arr;
                }
            }

            $data[$k] = array_combine($keys, $values);
        }

        return $data;
    }

    /**
     * 表格分页列表
     */
    public function getTableList($search = [], $page = 0, $limit = 0, $order = '')
    {
        $where = [];
        if ($search) {
            foreach ($search as $keyword => $value) {
                if ($value != '' && $keyword != 'page' && $keyword != 'limit') {
                    if ($keyword == 'raw' || $keyword == 'request' || $keyword == 'response') {
                        $where[] = [$keyword, 'like', '%'.trim($value).'%'];
                    }else{
                        $where[] = [$keyword, '=', trim($value)];
                    }
                }
            }
        }

        $dataQuery = self::where($where);

        if ($order) {
            $o = explode(',', $order);
            $dataQuery->order($o[0], trim($o[1]));
        }

        if ($page && $limit) {
            $tableData['list'] = $dataQuery->page($page, $limit)->order('id','desc')->select();
            $tableData['page'] = intval($page);
        }else{
            $tableData['list'] = $dataQuery->order('id','desc')->select();
        }
        $tableData['count'] = $dataQuery->count();

        return $tableData;
    }

    /**
     * 添加
     */
    public function add($data)
    {
        $data = self::transKey($data);
        return self::create($data);
    }

    /**
     * 修改
     */
    public function edit($id, $data)
    {
        $row = self::find($id);
        if (!$row) error('数据不存在或已删除');

        return $row->save(self::transKey($data));
    }

    /**
     * 删除
     */
    public function del($id)
    {
        $row = self::find($id);
        if (!$row) error('数据不存在或已被删除');
        return self::destroy($id);
    }

    /**
     * 批量删除
     */
    public function delBatch($idList)
    {
        $ids = [];
        foreach ($idList as $row)
        {
            validate(['id' => $row['id']],['id' => 'require|number|>:0']);
            $ids[] = $row['id'];
        }

        return self::destroy($ids);
    }
}
