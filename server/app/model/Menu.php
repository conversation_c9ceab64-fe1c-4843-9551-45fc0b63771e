<?php
/**
 * @author: xiaobo
 * @date: 2025-06-12
 * @description: 权限表模型
 */

namespace app\model;

use think\Model;
use app\model\traits\MigrateTrait;

/**
 * @mixin \think\Model
 */
class Menu extends Model
{
    use MigrateTrait;

    protected static $migrateSchema = [
        // 表配置
        'table' => [
            'engine' => 'InnoDB',
            'comment' => '权限表'
        ],

        // 字段
        'columns' => [
            ['title', 'string', ['limit' => 25, 'null' => false, 'comment' => '权限名称']],
            ['parent_id', 'integer', ['limit' => 10, 'default' => 0, 'comment' => '父级ID']],
            ['menu_type', 'boolean', ['default' => false, 'comment' => '菜单类型: 0 菜单, 1 按钮或列表权限']],
            ['open_type', 'boolean', ['default' => false, 'comment' => '打开方式: 1 组件, 2 内链, 3 外链']],
            ['icon', 'string', ['limit' => 40, 'default' => null, 'comment' => '图标']],
            ['hide', 'boolean', ['default' => false, 'comment' => '是否隐藏: 0 否, 1 是']],
            ['url_path', 'string', ['limit' => 80, 'default' => null, 'comment' => '路径地址']],
            ['api_path', 'string', ['limit' => 120, 'default' => null, 'comment' => '接口地址']],
            ['sort', 'integer', ['limit' => 4, 'default' => 0, 'comment' => '排序']],
            ['authority', 'string', ['limit' => 40, 'default' => null, 'comment' => '权限标识']],
            ['component', 'string', ['limit' => 40, 'default' => null, 'comment' => '组件']],
            ['redirect', 'string', ['limit' => 20, 'default' => null, 'comment' => '重定向']],
            ['meta', 'string', ['default' => null, 'comment' => '元数据']],
            ['update_time', 'datetime', ['default' => null, 'comment' => '更新时间']],
            ['create_time', 'datetime', ['default' => null, 'comment' => '创建时间']],
        ],

        // 索引
        'indexes' => [],
    ];
}