<?php
/**
 * @author: xiaobo
 * @date: 2025-06-12
 * @description: 角色表模型
 */

namespace app\model;

use think\Model;
use app\model\traits\MigrateTrait;

/**
 * @mixin \think\Model
 */
class Role extends Model
{
    use MigrateTrait;

    protected static $migrateSchema = [
        // 表配置
        'table' => [
            'engine' => 'InnoDB',
            'comment' => '角色表'
        ],

        // 字段
        'columns' => [
            ['name', 'string', ['limit' => 20, 'null' => false, 'comment' => '角色名称']],
            ['code', 'string', ['limit' => 20, 'null' => false, 'comment' => '角色标识']],
            ['comments', 'string', ['limit' => 100, 'default' => null, 'comment' => '备注']],
            ['update_time', 'datetime', ['default' => null, 'comment' => '更新时间']],
            ['create_time', 'datetime', ['default' => null, 'comment' => '创建时间']],
        ],

        // 索引
        'indexes' => [
            ['code'],
        ],
    ];
}