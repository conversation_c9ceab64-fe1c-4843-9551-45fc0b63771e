<?php
/**
 * @author: xiaobo
 * @date: 2025-06-12
 * @description: 角色菜单表模型
 */

namespace app\model;

use think\Model;
use app\model\traits\MigrateTrait;

/**
 * @mixin \think\Model
 */
class RoleMenu extends Model
{
    use MigrateTrait;

    protected $updateTime = false;

    protected static $migrateSchema = [
        // 表配置
        'table' => [
            'engine' => 'InnoDB',
            'comment' => '角色菜单表'
        ],

        // 字段
        'columns' => [
            ['role_id', 'integer', ['limit' => 10, 'null' => false, 'comment' => '角色ID']],
            ['menu_id', 'integer', ['limit' => 10, 'null' => false, 'comment' => '菜单ID']],
            ['create_time', 'datetime', ['default' => null, 'comment' => '创建时间']],
        ],

        // 索引
        'indexes' => [],
    ];
}