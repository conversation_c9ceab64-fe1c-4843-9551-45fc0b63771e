<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateRoleTable extends Migrator
{
    /**
     * Change Method.
     */
    public function change()
    {
        // 尝试创建表
        try {
            $table = $this->table('role', ['engine' => 'InnoDB', 'comment' => '角色表']);
            $table->addColumn('name', 'string', ['limit' => 20, 'null' => false, 'comment' => '角色名称'])
                ->addColumn('code', 'string', ['limit' => 20, 'null' => false, 'comment' => '角色标识'])
                ->addColumn('comments', 'string', ['limit' => 100, 'default' => NULL, 'comment' => '备注'])
                ->addColumn('update_time', 'datetime', ['default' => NULL, 'comment' => '更新时间'])
                ->addColumn('create_time', 'datetime', ['default' => NULL, 'comment' => '创建时间'])
                ->addIndex('code')
                ->create();
        } catch (\Exception $e) {
            // 表可能已经存在，尝试逐个添加缺失的字段和索引
            try {
                $table = $this->table('role');
                $table->addColumn('name', 'string', ['limit' => 20, 'null' => false, 'comment' => '角色名称'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('role');
                $table->addColumn('code', 'string', ['limit' => 20, 'null' => false, 'comment' => '角色标识'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('role');
                $table->addColumn('comments', 'string', ['limit' => 100, 'default' => NULL, 'comment' => '备注'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('role');
                $table->addColumn('update_time', 'datetime', ['default' => NULL, 'comment' => '更新时间'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('role');
                $table->addColumn('create_time', 'datetime', ['default' => NULL, 'comment' => '创建时间'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('role');
                $table->addIndex('code')->update();
            } catch (\Exception $e2) {
                // 索引可能已经存在或字段不存在
            }
        }
    }
}