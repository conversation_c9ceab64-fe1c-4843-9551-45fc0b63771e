<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateRoleMenuTable extends Migrator
{
    /**
     * Change Method.
     */
    public function change()
    {
        // 尝试创建表
        try {
            $table = $this->table('role_menu', ['engine' => 'InnoDB', 'comment' => '角色菜单表']);
            $table->addColumn('role_id', 'integer', ['limit' => 10, 'null' => false, 'comment' => '角色ID'])
                ->addColumn('menu_id', 'integer', ['limit' => 10, 'null' => false, 'comment' => '菜单ID'])
                ->addColumn('create_time', 'datetime', ['default' => NULL, 'comment' => '创建时间'])
                ->create();
        } catch (\Exception $e) {
            // 表可能已经存在，尝试逐个添加缺失的字段和索引
            try {
                $table = $this->table('role_menu');
                $table->addColumn('role_id', 'integer', ['limit' => 10, 'null' => false, 'comment' => '角色ID'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('role_menu');
                $table->addColumn('menu_id', 'integer', ['limit' => 10, 'null' => false, 'comment' => '菜单ID'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('role_menu');
                $table->addColumn('create_time', 'datetime', ['default' => NULL, 'comment' => '创建时间'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
        }
    }
}