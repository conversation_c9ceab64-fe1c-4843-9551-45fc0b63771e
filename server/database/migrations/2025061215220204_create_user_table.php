<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateUserTable extends Migrator
{
    /**
     * Change Method.
     */
    public function change()
    {
        // 尝试创建表
        try {
            $table = $this->table('user', ['engine' => 'InnoDB', 'comment' => '用户表']);
            $table->addColumn('username', 'string', ['limit' => 25, 'null' => false, 'default' => '', 'comment' => '用户名'])
                ->addColumn('nickname', 'string', ['limit' => 25, 'null' => false, 'default' => '', 'comment' => '昵称'])
                ->addColumn('password', 'string', ['limit' => 60, 'null' => false, 'default' => '', 'comment' => '用户密码'])
                ->addColumn('allow_login', 'boolean', ['default' => 1, 'comment' => '是否允许登录'])
                ->addColumn('last_login_ip', 'string', ['limit' => 20, 'default' => NULL, 'comment' => '最后登录IP'])
                ->addColumn('last_login_time', 'datetime', ['default' => NULL, 'comment' => '最后登录时间'])
                ->addColumn('update_time', 'datetime', ['default' => NULL, 'comment' => '更新时间'])
                ->addColumn('create_time', 'datetime', ['default' => NULL, 'comment' => '创建时间'])
                ->addIndex('username', ['unique' => true])
                ->create();
        } catch (\Exception $e) {
            // 表可能已经存在，尝试逐个添加缺失的字段和索引
            try {
                $table = $this->table('user');
                $table->addColumn('username', 'string', ['limit' => 25, 'null' => false, 'default' => '', 'comment' => '用户名'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('user');
                $table->addColumn('nickname', 'string', ['limit' => 25, 'null' => false, 'default' => '', 'comment' => '昵称'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('user');
                $table->addColumn('password', 'string', ['limit' => 60, 'null' => false, 'default' => '', 'comment' => '用户密码'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('user');
                $table->addColumn('allow_login', 'boolean', ['default' => 1, 'comment' => '是否允许登录'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('user');
                $table->addColumn('last_login_ip', 'string', ['limit' => 20, 'default' => NULL, 'comment' => '最后登录IP'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('user');
                $table->addColumn('last_login_time', 'datetime', ['default' => NULL, 'comment' => '最后登录时间'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('user');
                $table->addColumn('update_time', 'datetime', ['default' => NULL, 'comment' => '更新时间'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('user');
                $table->addColumn('create_time', 'datetime', ['default' => NULL, 'comment' => '创建时间'])->update();
            } catch (\Exception $e2) {
                // 字段可能已经存在
            }
            try {
                $table = $this->table('user');
                $table->addIndex('username', ['unique' => true])->update();
            } catch (\Exception $e2) {
                // 索引可能已经存在或字段不存在
            }
        }
    }
}