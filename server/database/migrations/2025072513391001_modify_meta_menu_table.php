<?php

use think\migration\Migrator;
use think\migration\db\Column;

class ModifyMetaMenuTable extends Migrator
{
    /**
     * Change Method.
     */
    public function change()
    {
        // 字段 meta 的具体变化：
        // 注意：此处显示的是新的字段定义，具体变化请参考Model定义
        // 修改字段：meta
        try {
            $table = $this->table('menu');
            if (method_exists($table, 'hasColumn') && $table->hasColumn('meta')) {
                $table->changeColumn('meta', 'string', ['default' => NULL, 'comment' => '元数据'])->update();
                echo "[SUCCESS] 字段 meta 已成功修改\n";
            } else {
                echo "[SKIP] 字段 meta 不存在，修改操作已跳过\n";
            }
        } catch (\Exception $e) {
            echo "[WARNING] 修改字段 meta 失败：" . $e->getMessage() . "，操作已跳过\n";
        }

    }
}