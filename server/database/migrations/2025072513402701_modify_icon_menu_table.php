<?php

use think\migration\Migrator;
use think\migration\db\Column;

class ModifyIconMenuTable extends Migrator
{
    /**
     * Change Method.
     */
    public function change()
    {
        // 字段 icon 的具体变化：
        // 注意：此处显示的是新的字段定义，具体变化请参考Model定义
        // 修改字段：icon
        try {
            $table = $this->table('menu');
            if (method_exists($table, 'hasColumn') && $table->hasColumn('icon')) {
                $table->changeColumn('icon', 'string', ['limit' => 40, 'default' => NULL, 'comment' => '图标'])->update();
                echo "[SUCCESS] 字段 icon 已成功修改\n";
            } else {
                echo "[SKIP] 字段 icon 不存在，修改操作已跳过\n";
            }
        } catch (\Exception $e) {
            echo "[WARNING] 修改字段 icon 失败：" . $e->getMessage() . "，操作已跳过\n";
        }

    }
}