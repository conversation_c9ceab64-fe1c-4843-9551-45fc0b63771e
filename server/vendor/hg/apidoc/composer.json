{"name": "hg/apidoc", "description": "根据注解生成API文档，兼容Laravel、ThinkPHP、Hyperf、Webman等框架；在线调试、Markdown文档、多应用/多版本、Mock数据、授权访问、接口生成器、代码生成器等众多实用功能", "keywords": ["apidoc", "api文档", "接口文档", "自动生成api", "注释生成", "php接口文档", "api文档", "<PERSON><PERSON>", "注解"], "require": {"php": "^7.1 || ^8.0", "doctrine/annotations": "^1 || ^2"}, "license": "MIT", "authors": [{"name": "hg-code", "email": "<EMAIL>"}], "autoload": {"psr-4": {"hg\\apidoc\\": "src/"}}, "extra": {"laravel": {"providers": ["hg\\apidoc\\providers\\LaravelService"]}, "think": {"services": ["hg\\apidoc\\providers\\ThinkPHPService"], "config": {"apidoc": "src/config.php"}}, "hyperf": {"config": "hg\\apidoc\\ConfigProvider"}}, "minimum-stability": "dev"}