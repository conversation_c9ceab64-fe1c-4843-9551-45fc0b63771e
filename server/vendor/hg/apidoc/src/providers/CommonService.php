<?php

namespace hg\apidoc\providers;

use hg\apidoc\utils\ConfigProvider;

class CommonService
{
    use BaseService;

    static function getApidocConfig()
    {
        // TODO: Implement getApidocConfig() method.
    }

    static function registerRoute($route)
    {
        // TODO: Implement registerRoute() method.
    }

    static function databaseQuery($sql)
    {
        // TODO: Implement databaseQuery() method.
    }

    static function getRootPath()
    {
        // TODO: Implement getRootPath() method.
    }

    static function getRuntimePath()
    {
        // TODO: Implement getRuntimePath() method.
    }

    static function setLang($locale)
    {
        // TODO: Implement setLang() method.
    }

    static function getLang($lang)
    {
        // TODO: Implement getLang() method.
    }

    static function handleResponseJson($res)
    {
        // TODO: Implement handleResponseJson() method.
    }

    static function getTablePrefix()
    {
        // TODO: Implement getTablePrefix() method.
    }

}