{"version": 3, "sources": ["../../ele-admin-plus/es/ele-pro-form/props.js"], "sourcesContent": ["import { formProps, formEmits } from \"element-plus\";\nimport { pick } from \"../utils/common\";\nconst proFormProps = {\n  ...formProps,\n  /** 标签宽度 */\n  labelWidth: {\n    type: [String, Number],\n    default: \"80px\"\n  },\n  /** 表单项 */\n  items: Array,\n  /** 是否栅格布局 */\n  grid: [Boolean, Object],\n  /** 栅格布局时更多 ElRow 属性 */\n  rowProps: Object,\n  /** 是否需要底栏 */\n  footer: Boolean,\n  /** 底栏 ElFormItem 属性 */\n  footerProps: Object,\n  /** 底栏 ElFormItem 插槽 */\n  footerSlots: Object,\n  /** 栅格布局时底栏 ElCol 属性 */\n  footerColProps: Object,\n  /** 自动计算底栏栅格份数 */\n  autoFooterCol: Boolean,\n  /** 底栏样式 */\n  footerStyle: Object,\n  /** 提交按钮文本 */\n  submitText: String,\n  /** 重置按钮文本 */\n  resetText: String,\n  /** 提交按钮属性 */\n  submitButtonProps: Object,\n  /** 重置按钮属性 */\n  resetButtonProps: Object,\n  /** 是否在底栏显示表单展开收起按钮 */\n  showSearchExpand: Boolean,\n  /** 展开和收起按钮属性 */\n  searchExpandButtonProps: Object,\n  /** 展开按钮的文字 */\n  searchExpandText: String,\n  /** 收起按钮的文字 */\n  searchShrinkText: String,\n  /** 搜索表单展开状态 */\n  searchExpand: Boolean,\n  /** 阻止表单原生的表单提交事件 */\n  preventFormSubmit: {\n    type: Boolean,\n    default: true\n  },\n  /** 编辑模式 */\n  editable: Boolean,\n  /** 屏幕尺寸 */\n  screenSize: String,\n  /** 编辑模式选中的表单项 */\n  activeItemKey: [String, Number],\n  /** 组件类型数据 */\n  itemTypeData: Array,\n  /** 远程数据源请求工具 */\n  httpRequest: [Object, Function],\n  /** 国际化 */\n  locale: Object\n};\nconst proFormEmits = {\n  ...formEmits,\n  \"update:searchExpand\": (_expand) => true,\n  updateValue: (_prop, _value) => true,\n  \"update:items\": (_items) => true,\n  \"update:activeItemKey\": (_activeKey) => true,\n  submit: (_model) => true,\n  reset: () => true\n};\nconst childrenRenderProps = {\n  ...pick(proFormProps, [\n    \"model\",\n    \"items\",\n    \"rules\",\n    \"grid\",\n    \"rowProps\",\n    \"editable\",\n    \"screenSize\",\n    \"activeItemKey\",\n    \"itemTypeData\",\n    \"httpRequest\"\n  ]),\n  /** 额外的 ElCol 属性 */\n  contentExtraColProps: Object,\n  /** 自动计算额外的 ElCol 份数 */\n  autoContentExtraCol: {\n    type: Boolean,\n    required: false\n  },\n  /** 父级表单项 */\n  parentItem: Object,\n  /** 全部的表单项 */\n  formItems: Array,\n  /** 搜索表单展开状态 */\n  searchExpand: Boolean,\n  /** 编辑模式禁用子级排序 */\n  sortDisabled: Boolean,\n  /** 编辑模式父级拖拽容器是否可点击选中 */\n  containerSelectable: Boolean,\n  /** 直接传递插槽数据 */\n  slots: Object,\n  /** 获取表单组件的组件引用数据方法 */\n  getProFormRefs: Function,\n  /** 获取并缓存代码解析结果方法 */\n  getAndCacheCode: Function,\n  /** 更新表单数据属性值方法 */\n  updateItemValue: Function,\n  /** 编辑模式更新表单项数据方法 */\n  updateItemsData: Function,\n  /** 更新编辑模式选中项方法 */\n  updateActiveItemKey: Function,\n  /** 默认的必填提示文本 */\n  requiredLang: String,\n  /** 兼容旧版 */\n  item: Object\n};\nexport {\n  childrenRenderProps,\n  proFormEmits,\n  proFormProps\n};\n"], "mappings": ";;;;;;;;;AAEA,IAAM,eAAe;AAAA,EACnB,GAAG;AAAA;AAAA,EAEH,YAAY;AAAA,IACV,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,EAEP,MAAM,CAAC,SAAS,MAAM;AAAA;AAAA,EAEtB,UAAU;AAAA;AAAA,EAEV,QAAQ;AAAA;AAAA,EAER,aAAa;AAAA;AAAA,EAEb,aAAa;AAAA;AAAA,EAEb,gBAAgB;AAAA;AAAA,EAEhB,eAAe;AAAA;AAAA,EAEf,aAAa;AAAA;AAAA,EAEb,YAAY;AAAA;AAAA,EAEZ,WAAW;AAAA;AAAA,EAEX,mBAAmB;AAAA;AAAA,EAEnB,kBAAkB;AAAA;AAAA,EAElB,kBAAkB;AAAA;AAAA,EAElB,yBAAyB;AAAA;AAAA,EAEzB,kBAAkB;AAAA;AAAA,EAElB,kBAAkB;AAAA;AAAA,EAElB,cAAc;AAAA;AAAA,EAEd,mBAAmB;AAAA,IACjB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,UAAU;AAAA;AAAA,EAEV,YAAY;AAAA;AAAA,EAEZ,eAAe,CAAC,QAAQ,MAAM;AAAA;AAAA,EAE9B,cAAc;AAAA;AAAA,EAEd,aAAa,CAAC,QAAQ,QAAQ;AAAA;AAAA,EAE9B,QAAQ;AACV;AACA,IAAM,eAAe;AAAA,EACnB,GAAG;AAAA,EACH,uBAAuB,CAAC,YAAY;AAAA,EACpC,aAAa,CAAC,OAAO,WAAW;AAAA,EAChC,gBAAgB,CAAC,WAAW;AAAA,EAC5B,wBAAwB,CAAC,eAAe;AAAA,EACxC,QAAQ,CAAC,WAAW;AAAA,EACpB,OAAO,MAAM;AACf;AACA,IAAM,sBAAsB;AAAA,EAC1B,GAAG,KAAK,cAAc;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAAA;AAAA,EAED,sBAAsB;AAAA;AAAA,EAEtB,qBAAqB;AAAA,IACnB,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,WAAW;AAAA;AAAA,EAEX,cAAc;AAAA;AAAA,EAEd,cAAc;AAAA;AAAA,EAEd,qBAAqB;AAAA;AAAA,EAErB,OAAO;AAAA;AAAA,EAEP,gBAAgB;AAAA;AAAA,EAEhB,iBAAiB;AAAA;AAAA,EAEjB,iBAAiB;AAAA;AAAA,EAEjB,iBAAiB;AAAA;AAAA,EAEjB,qBAAqB;AAAA;AAAA,EAErB,cAAc;AAAA;AAAA,EAEd,MAAM;AACR;", "names": []}